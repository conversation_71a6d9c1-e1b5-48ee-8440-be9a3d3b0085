<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Winner Cash - Herbal Lucky Draw</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: #1e3c72;
            color: white;
            padding: 10px 0;
            font-size: 12px;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .contact-info {
            display: flex;
            gap: 30px;
        }

        .contact-info span {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        /* Navigation */
        .navbar {
            background: white;
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo img {
            width: 40px;
            height: 40px;
        }

        .logo-text {
            font-size: 18px;
            font-weight: bold;
            color: #2a5298;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-menu a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            font-size: 14px;
            text-transform: uppercase;
            transition: color 0.3s ease;
        }

        .nav-menu a:hover {
            color: #2a5298;
        }

        /* Hero Section */
        .hero-section {
            background: url('images/banner-bg.jpg') center/cover;
            background-color: #ff9800;
            padding: 60px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 152, 0, 0.1);
        }

        .hero-title {
            font-size: 48px;
            font-weight: bold;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        /* Winner Info Card */
        .winner-card {
            max-width: 600px;
            margin: -30px auto 40px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
            position: relative;
            z-index: 2;
        }

        .winner-info {
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #eee;
        }

        .winner-name {
            font-size: 16px;
            color: #333;
        }

        .prize-amount {
            font-size: 18px;
            font-weight: bold;
            color: #2a5298;
        }

        .copy-icon {
            margin-left: 10px;
            cursor: pointer;
            color: #666;
            transition: color 0.3s ease;
        }

        .copy-icon:hover {
            color: #2a5298;
        }

        /* Process Icons */
        .process-icons {
            display: flex;
            justify-content: space-around;
            padding: 20px;
            background: #2a5298;
        }

        .process-icon {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: white;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .process-icon:hover {
            transform: translateY(-5px);
        }

        .process-icon i {
            font-size: 24px;
            margin-bottom: 8px;
            padding: 15px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            width: 54px;
            height: 54px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .process-icon span {
            font-size: 12px;
            text-align: center;
        }

        /* Form Section */
        .form-section {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
            margin-bottom: 40px;
        }

        .form-header {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }

        .form-title {
            font-size: 18px;
            color: #333;
            margin-bottom: 10px;
        }

        .form-subtitle {
            font-size: 16px;
            color: #666;
            font-weight: 500;
        }

        .form-content {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
            font-size: 14px;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #2a5298;
            box-shadow: 0 0 0 3px rgba(42, 82, 152, 0.1);
        }

        .submit-btn {
            width: 100%;
            background: linear-gradient(45deg, #ff9800, #ffc107);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 152, 0, 0.3);
        }

        /* Footer */
        .footer {
            background: #1e3c72;
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 40px;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .footer h3 {
            margin-bottom: 10px;
            font-size: 18px;
        }

        .footer p {
            font-size: 12px;
            opacity: 0.8;
        }

        /* WhatsApp Float */
        .whatsapp-float {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #25d366;
            color: white;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
            cursor: pointer;
            transition: transform 0.3s ease;
            z-index: 1000;
        }

        .whatsapp-float:hover {
            transform: scale(1.1);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 10px;
            }

            .contact-info {
                gap: 15px;
                font-size: 11px;
            }

            .nav-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-menu {
                gap: 15px;
                flex-wrap: wrap;
                justify-content: center;
            }

            .hero-title {
                font-size: 32px;
            }

            .winner-card, .form-section {
                margin: 20px;
            }

            .process-icons {
                flex-wrap: wrap;
                gap: 10px;
            }

            .process-icon {
                flex: 1;
                min-width: 80px;
            }

            .form-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="contact-info">
                <span><i class="fas fa-phone"></i> SUPPORT NUMBER: +************</span>
                <span><i class="fas fa-whatsapp"></i> What's App Number: +************</span>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-content">
            <div class="logo">
                <img src="images/logo.png" alt="Ayurveda Logo">
                <span class="logo-text">Ayurveda</span>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html">PRODUCTS</a></li>
                <li><a href="Prize.html">PRIZE</a></li>
                <li><a href="How-to-Win.html">HOW TO WIN</a></li>
                <li><a href="Winner-List.html">WINNER LIST</a></li>
                <li><a href="Status.html">CHECK STATUS</a></li>
                <li><a href="Terms.html">TERMS & CONDITIONS</a></li>
                <li><a href="Contact.html">CONTACT</a></li>
            </ul>
        </div>
    </nav>

    <!-- Hero Section -->
    <div class="hero-section">
        <h1 class="hero-title">Winner-cash</h1>
    </div>

    <!-- Winner Info Card -->
    <div class="winner-card">
        <div class="winner-info">
            <span class="winner-name">Name:</span>
            <div>
                <span class="prize-amount">Prize Amount ₹ 6,30,000.00</span>
                <i class="fas fa-copy copy-icon" onclick="copyPrizeAmount()"></i>
            </div>
        </div>
        
        <!-- Process Icons -->
        <div class="process-icons">
            <div class="process-icon" onclick="sendBankDetails()">
                <i class="fas fa-university"></i>
                <span>Send Bank Details</span>
            </div>
            <div class="process-icon" onclick="requestMoney()">
                <i class="fas fa-money-bill-wave"></i>
                <span>Request Money</span>
            </div>
            <div class="process-icon" onclick="billPay()">
                <i class="fas fa-receipt"></i>
                <span>Bill Pay</span>
            </div>
            <div class="process-icon" onclick="qrCode()">
                <i class="fas fa-qrcode"></i>
                <span>QR Code</span>
            </div>
            <div class="process-icon" onclick="other()">
                <i class="fas fa-ellipsis-h"></i>
                <span>Other</span>
            </div>
        </div>
    </div>

    <!-- Form Section -->
    <div class="form-section">
        <div class="form-header">
            <h2 class="form-title">Send your request to Prize Department</h2>
            <p class="form-subtitle">Send your request to Prize Department</p>
        </div>
        
        <div class="form-content">
            <form id="prizeRequestForm">
                <div class="form-group">
                    <label for="accountHolderName">Account Holder's Name</label>
                    <input type="text" id="accountHolderName" name="accountHolderName" placeholder="Account Holder's Name" required>
                </div>
                
                <div class="form-group">
                    <label for="accountNo">Account No</label>
                    <input type="text" id="accountNo" name="accountNo" placeholder="Account No." required>
                </div>
                
                <div class="form-group">
                    <label for="ifscCode">IFSC Code</label>
                    <input type="text" id="ifscCode" name="ifscCode" placeholder="IFSC Code" required>
                </div>
                
                <div class="form-group">
                    <label for="bankName">Bank Name</label>
                    <input type="text" id="bankName" name="bankName" placeholder="Bank Name" required>
                </div>
                
                <div class="form-group">
                    <label for="mobileNo">Mobile No</label>
                    <input type="tel" id="mobileNo" name="mobileNo" placeholder="Mobile No" required>
                </div>
                
                <div class="form-group">
                    <label for="alternateContact">Alternate Contact No</label>
                    <input type="tel" id="alternateContact" name="alternateContact" placeholder="Alternate Contact No.">
                </div>
                
                <div class="form-group">
                    <label for="prize">Prize</label>
                    <input type="text" id="prize" name="prize" placeholder="Prize" value="₹ 6,30,000.00" readonly>
                </div>
                
                <div class="form-group">
                    <label for="date">Date</label>
                    <input type="text" id="date" name="date" placeholder="Friday 1st of August 2025 01:30:13 PM" readonly>
                </div>
                
                <button type="submit" class="submit-btn">Submit</button>
            </form>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <div class="footer-content">
            <h3>Head office : <i class="fas fa-phone"></i> +************</h3>
            <p>Copyright © 2025-2026 All Rights Reserved</p>
        </div>
    </div>

    <!-- WhatsApp Float -->
    <div class="whatsapp-float" onclick="openWhatsApp()">
        <i class="fab fa-whatsapp"></i>
    </div>

    <script>
        // Set current date and time
        document.addEventListener('DOMContentLoaded', function() {
            const now = new Date();
            const options = { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: true
            };
            const dateString = now.toLocaleDateString('en-US', options);
            document.getElementById('date').value = dateString;
        });

        // Copy prize amount
        function copyPrizeAmount() {
            const prizeAmount = "₹ 6,30,000.00";
            navigator.clipboard.writeText(prizeAmount).then(function() {
                alert('Prize amount copied to clipboard!');
            });
        }

        // Process icon functions
        function sendBankDetails() {
            alert('Send Bank Details selected');
        }

        function requestMoney() {
            alert('Request Money selected');
        }

        function billPay() {
            alert('Bill Pay selected');
        }

        function qrCode() {
            alert('QR Code selected');
        }

        function other() {
            alert('Other option selected');
        }

        // Form submission
        document.getElementById('prizeRequestForm').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Your request has been submitted successfully!');
        });

        // WhatsApp function
        function openWhatsApp() {
            window.open('https://wa.me/************', '_blank');
        }
    </script>
</body>
</html>
